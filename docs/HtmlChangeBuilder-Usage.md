# High-Performance HTML Change Builder

## Overview

The new `HtmlChangeBuilder` provides a much more concise and high-performance way to generate `HtmlChange` objects from entity comparisons. Instead of requiring individual strategy classes for each field, you can now use a simple JSON configuration to map field names to change IDs.

## Key Features

- **JSON-driven configuration**: Define field mappings in JSON format
- **High-performance direct method calls**: Uses pre-compiled function mappings instead of reflection
- **Simplified logic**: No need for individual strategy classes
- **Flexible field access**: Supports both direct entity fields and nested CrawlerResponse fields
- **Zero reflection overhead**: Optimized for high-throughput scenarios

## Usage Examples

### 1. Basic JSON Mapping

```java
String fieldMappingJson = """
{
  "title": 89,
  "description": 17,
  "h1": 29,
  "h2": 34,
  "contentType": 45,
  "followFlg": 52
}
""";

List<HtmlChange> changes = HtmlChangeBuilder.buildFromJson(previous, current, fieldMappingJson);
```

### 2. Using Map Configuration

```java
Map<String, Integer> fieldMappings = new HashMap<>();
fieldMappings.put("title", 89);
fieldMappings.put("description", 17);
fieldMappings.put("h1", 29);

List<HtmlChange> changes = HtmlChangeBuilder.buildFromMap(previous, current, fieldMappings);
```

### 3. Using Predefined Sample Mappings

```java
Map<String, Integer> sampleMappings = HtmlChangeBuilder.createSampleFieldMappings();
List<HtmlChange> changes = HtmlChangeBuilder.buildFromMap(previous, current, sampleMappings);
```

## Field Name Resolution

The system uses pre-compiled function mappings for maximum performance:

1. **For Current Entity (HtmlClickHouseEntity)**:
   - Uses direct method calls to `crawlerResponse` object for most fields
   - Falls back to main entity object for entity-specific fields

2. **For Previous Entity (UrlMetricsEntityV3)**:
   - Uses direct method calls to entity getter methods

3. **Performance Optimization**:
   - All field mappings are pre-compiled at initialization
   - No reflection overhead during runtime
   - Direct lambda function calls for field access
   - Supports both camelCase and snake_case field names

## Supported Field Types

The system automatically converts various field types to strings:

- **String**: Direct conversion
- **Boolean**: Converted to "true"/"false"
- **Number**: Converted to string representation
- **Arrays**: Serialized to JSON
- **Objects**: Serialized to JSON
- **Dates**: Converted to string representation

## Common Field Mappings

Here are the most commonly used field mappings with their corresponding change IDs:

| Field Name | Change ID | Description |
|------------|-----------|-------------|
| title | 89 | Page title changes |
| description | 17 | Meta description changes |
| h1 | 29 | H1 tag changes |
| h2 | 34 | H2 tag changes |
| contentType | 45 | Content type changes |
| followFlg | 52 | Follow flag changes |
| indexable | 48 | Indexable status changes |
| canonical | 39 | Canonical URL changes |
| amphtmlHref | 2 | AMP HTML href changes |
| responseCode | 80 | HTTP response code changes |

## Migration from Old Approach

### Old Approach (Complex)
```java
// Required individual strategy classes for each field
public class TitleChgIndicatorStrategyImpl extends TitleIndicatorStrategyImpl {
    @Override
    public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
        return BooleanUtils.isTrue(htmlClickHouseEntity.getTitleChgInd());
    }

    @Override
    public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
        temp.setChgId(ChangeIndicatorEnum.TITLE_CHG_IND.id);
        temp.setPrevValue(previous.getTitle());
        temp.setCurrValue(current.getCrawlerResponse().getTitle());
        return temp;
    }
}
```

### New Approach (Simple)
```java
// Just define the mapping in JSON
String mapping = """
{
  "title": 89
}
""";

List<HtmlChange> changes = HtmlChangeBuilder.buildFromJson(previous, current, mapping);
```

## Error Handling

The system includes robust error handling:

- **Field Not Found**: Logs a warning and continues with other fields
- **Type Conversion Errors**: Logs error details and skips the problematic field
- **Reflection Errors**: Gracefully handles access issues and continues processing

## Performance Benefits

- **Reduced Code**: Eliminates need for 100+ strategy classes
- **Zero Reflection Overhead**: Uses pre-compiled function mappings for maximum performance
- **Direct Method Calls**: No runtime method lookup or invocation overhead
- **Optimized for High-Throughput**: Designed for processing large volumes of entities
- **Faster Development**: Add new field mappings without writing new classes
- **Easier Maintenance**: All mappings in one place
- **Dynamic Configuration**: Can load mappings from external configuration files

### Performance Comparison

| Approach | Method Lookup | Runtime Overhead | Throughput |
|----------|---------------|------------------|------------|
| Old Strategy Classes | Compile-time | Low | High |
| Reflection-based | Runtime | High | Low |
| **New Function Mapping** | **Compile-time** | **Minimal** | **Highest** |

## Testing

Run the included test to verify functionality:

```bash
mvn test -Dtest=HtmlChangeBuilderTest
```

The test covers:
- JSON mapping functionality
- Map-based mapping
- Field value extraction
- Error handling scenarios
