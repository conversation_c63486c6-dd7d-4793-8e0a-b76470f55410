2025-05-27 11:20:58.961 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 0
2025-05-27 11:20:58.969 [main] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:20:58.970 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 10
2025-05-27 11:20:58.971 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 4
2025-05-27 11:21:07.717 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - === Example 1: Basic JSON Mapping ===
2025-05-27 11:21:07.741 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 4
2025-05-27 11:21:07.742 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Generated 4 changes:
2025-05-27 11:21:07.742 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Change ID: 89, Prev: 'Original Page Title', Curr: 'Updated Page Title - Now with More Keywords'
2025-05-27 11:21:07.742 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Change ID: 17, Prev: 'Original meta description for the page', Curr: 'Updated meta description with better SEO optimizat...'
2025-05-27 11:21:07.742 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Change ID: 29, Prev: '["Original H1 Heading"]', Curr: '["Updated H1 Heading"]'
2025-05-27 11:21:07.742 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Change ID: 34, Prev: '["Original H2 Section 1","Original H2 Section 2"]', Curr: '["Updated H2 Section 1","Updated H2 Section 2","Ne...'
2025-05-27 11:21:07.742 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - 
2025-05-27 11:21:07.743 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - === Example 2: Comprehensive Field Mapping ===
2025-05-27 11:21:07.743 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.744 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.744 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Generated 10 changes using sample mappings:
2025-05-27 11:21:07.744 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Change ID: 2, URL: https://example.com/page
2025-05-27 11:21:07.744 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Change ID: 52, URL: https://example.com/page
2025-05-27 11:21:07.744 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Change ID: 48, URL: https://example.com/page
2025-05-27 11:21:07.744 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Change ID: 17, URL: https://example.com/page
2025-05-27 11:21:07.744 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Change ID: 29, URL: https://example.com/page
2025-05-27 11:21:07.744 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Change ID: 34, URL: https://example.com/page
2025-05-27 11:21:07.745 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Change ID: 39, URL: https://example.com/page
2025-05-27 11:21:07.745 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Change ID: 89, URL: https://example.com/page
2025-05-27 11:21:07.745 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Change ID: 45, URL: https://example.com/page
2025-05-27 11:21:07.745 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Change ID: 80, URL: https://example.com/page
2025-05-27 11:21:07.745 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - 
2025-05-27 11:21:07.745 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - === Example 3: Custom Field Selection ===
2025-05-27 11:21:07.746 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.746 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 5
2025-05-27 11:21:07.746 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Generated 5 SEO-focused changes:
2025-05-27 11:21:07.746 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - SEO Change - ID: 52, Domain: 12345
2025-05-27 11:21:07.746 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - SEO Change - ID: 48, Domain: 12345
2025-05-27 11:21:07.746 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - SEO Change - ID: 17, Domain: 12345
2025-05-27 11:21:07.746 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - SEO Change - ID: 39, Domain: 12345
2025-05-27 11:21:07.746 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - SEO Change - ID: 89, Domain: 12345
2025-05-27 11:21:07.746 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - 
2025-05-27 11:21:07.746 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - === Example 4: Performance Demonstration ===
2025-05-27 11:21:07.747 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.747 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.747 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.747 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.747 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.747 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.747 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.747 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.747 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.748 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.748 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.748 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.748 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.748 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.748 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.748 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.748 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.749 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.749 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.749 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.749 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.749 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.749 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.750 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.750 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.750 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.750 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.750 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.750 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.750 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.751 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.751 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.751 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.751 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.751 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.751 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.751 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.751 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.751 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.753 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.753 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.753 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.753 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.753 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.753 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.753 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.754 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.754 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.754 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.754 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.754 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.754 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.754 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.754 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.755 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.755 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.755 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.755 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.755 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.755 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.755 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.755 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.755 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.756 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.756 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.756 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.756 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.756 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.756 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.756 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.756 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.756 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.757 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.757 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.757 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.757 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.757 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.757 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.757 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.758 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.758 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.758 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.758 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.758 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.758 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.758 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.758 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.759 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.759 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.759 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.759 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.759 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.759 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.759 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.760 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.760 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.760 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.760 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.760 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.760 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.760 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.760 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.760 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.761 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.761 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.761 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.761 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.761 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.761 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.761 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.761 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.761 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.761 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.761 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.762 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.762 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.762 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.762 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.762 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.762 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.762 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.762 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.762 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.763 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.763 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.763 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.763 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.763 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.763 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.763 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.763 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.763 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.764 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.764 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.764 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.764 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.764 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.764 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.764 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.764 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.764 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.764 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.765 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.765 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.765 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.765 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.765 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.773 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.773 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.774 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.774 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.774 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.774 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.774 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.774 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.774 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.774 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.774 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.775 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.775 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.775 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.775 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.775 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.775 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.775 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.775 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.775 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.775 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.776 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.776 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.776 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.776 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.776 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.776 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.776 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.776 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.776 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.777 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.777 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.777 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.777 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.777 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.777 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.777 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.777 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.777 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.777 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.777 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.778 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.778 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.778 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.778 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.778 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.778 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.778 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.778 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.778 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.778 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.778 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.778 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.779 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.779 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.779 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.779 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.779 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.779 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.779 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.779 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.779 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.779 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.779 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.780 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.780 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.780 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.780 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.780 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.782 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.782 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.782 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.782 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.782 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.782 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.783 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.783 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.783 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.783 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.783 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.783 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.783 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.783 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.783 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.783 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.783 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.783 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.784 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.784 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.784 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.784 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.784 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.784 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.784 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.784 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.784 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.784 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.784 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.784 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.785 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.785 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.785 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.785 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.785 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.785 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.785 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.785 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.785 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.785 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.785 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.785 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.785 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.786 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.786 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.786 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.786 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.786 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.786 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.786 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.786 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.786 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.787 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.787 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.787 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.787 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.787 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.787 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.787 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.787 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.787 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.787 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.787 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.787 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.787 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.787 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.788 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.788 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.788 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.788 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.788 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.788 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.788 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.788 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.788 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.788 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.788 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.789 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.789 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.789 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.789 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.790 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.790 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.790 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.791 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.791 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.791 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.791 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.791 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.791 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.791 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.791 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.791 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.791 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.791 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.791 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.791 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.792 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.792 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.792 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.792 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.792 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.793 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.793 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.793 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.793 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.793 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.793 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.793 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.793 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.793 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.793 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.793 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.793 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.794 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.794 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.794 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.794 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.794 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.794 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.794 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.794 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.794 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.794 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.794 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.794 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.794 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.794 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.795 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.795 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.795 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.795 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.795 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.795 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.795 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.795 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.795 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.795 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.795 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.795 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.795 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.796 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.796 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.796 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.796 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.796 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.796 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.796 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.796 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.796 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.796 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.796 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.796 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.796 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.796 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.796 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.797 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.797 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.797 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.797 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.797 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.797 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.797 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.797 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.797 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.797 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.797 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.797 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.798 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.798 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.798 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.798 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.798 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.798 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.798 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.798 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.798 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.798 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.798 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.798 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.798 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.798 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.798 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.799 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.799 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.799 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.799 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.799 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.799 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.799 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.799 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.799 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.799 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.799 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.799 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.799 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.799 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.799 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.800 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.800 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.800 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.800 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.800 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.800 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.800 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.800 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.800 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.800 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.800 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.800 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.800 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.800 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.800 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.801 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.802 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.803 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.804 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.805 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.806 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.807 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.808 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.809 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.810 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.810 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.810 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.810 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.810 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.810 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.810 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.810 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.810 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.810 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.810 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.811 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.812 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.813 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.814 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.815 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.816 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.817 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.817 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.817 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.817 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.817 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.818 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.818 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.818 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.818 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.818 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.818 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.818 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.818 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.818 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.818 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.819 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.820 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.821 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.822 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.823 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.824 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.825 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.826 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.826 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.826 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.826 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.826 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.826 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.826 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.826 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.826 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.826 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.826 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.826 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.826 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.826 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.826 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.827 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.828 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.829 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.829 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.831 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.832 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.833 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.839 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.839 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.839 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.839 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.839 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.839 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.839 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.839 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.839 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.839 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.839 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.839 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.839 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.839 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.839 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.840 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.840 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.840 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.840 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.840 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.840 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.840 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.840 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.840 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.840 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.840 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.840 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.841 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.842 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.844 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.844 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.844 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.844 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.844 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.844 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.844 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.844 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.844 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.844 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.844 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.844 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.844 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.844 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.844 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.846 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.846 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.846 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.846 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.846 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.846 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.846 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.846 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.846 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.846 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.846 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.846 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.846 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.846 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.846 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.849 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.850 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.851 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.852 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.852 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.855 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.856 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.865 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.870 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.870 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.870 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.870 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.870 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.870 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.870 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.870 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.870 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.873 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.873 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.873 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.873 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.873 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.873 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.873 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.880 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.880 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.880 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.880 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.880 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.880 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.880 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.880 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.881 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.891 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.891 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.891 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.892 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.892 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.892 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.892 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.907 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.907 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.907 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.907 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.907 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.907 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.907 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.907 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.913 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.914 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.915 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.928 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.928 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.928 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.928 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.928 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.928 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.928 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.941 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.941 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.941 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.941 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.941 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.941 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.941 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.941 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.941 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.941 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.941 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.941 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.941 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.941 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.941 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.942 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.942 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.942 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.942 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.942 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.942 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.944 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.944 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.944 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.944 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.944 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.944 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.944 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.944 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.944 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.944 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.944 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.947 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.947 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.947 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.947 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.947 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.947 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.947 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.947 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.947 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.947 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.947 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.947 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.947 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.948 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.948 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.948 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.948 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.948 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.948 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.949 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.949 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.949 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.949 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.949 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.949 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.949 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.949 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.949 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.949 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.949 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.949 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.950 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.950 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.955 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.956 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.956 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.956 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.956 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.956 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.956 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.969 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.969 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.969 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.972 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.972 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.972 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.972 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.972 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.977 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.977 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.977 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.977 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.977 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.977 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.977 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.978 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.978 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.978 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.978 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.978 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.978 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.978 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.978 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.978 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.978 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.978 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.978 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.978 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.978 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.978 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.979 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.979 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.979 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.983 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.984 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.985 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.986 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.987 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.988 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.988 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.988 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.988 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.988 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.989 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.989 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.989 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.989 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.989 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.989 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.989 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.989 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.990 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.994 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.994 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.994 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.994 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.994 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.994 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.994 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.994 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.994 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.994 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.994 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.994 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.994 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.994 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.994 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.997 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:07.998 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.000 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.001 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.002 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.003 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.003 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.003 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.003 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.003 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.003 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.003 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.003 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.003 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.005 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.006 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.006 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.006 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.006 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.006 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.006 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.006 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.006 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.006 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.006 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.006 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.006 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.006 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.006 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.006 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.007 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.007 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.007 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.007 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.007 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.007 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.007 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.007 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.008 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.009 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.010 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.010 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.010 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.011 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.011 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.011 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.011 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.011 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.011 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.011 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.011 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.011 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.011 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.011 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.011 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.012 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.012 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.012 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.012 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.012 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.012 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.012 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.012 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.012 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.012 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.012 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.013 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.014 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.015 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.016 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.017 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.018 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.018 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.019 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.019 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.019 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.019 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.019 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.019 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.019 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.019 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.020 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.020 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.020 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.020 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.020 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.020 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.020 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.021 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.024 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.024 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.024 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.024 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.024 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.024 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.024 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.025 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.026 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.027 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.028 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.029 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.029 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.029 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.029 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.029 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.029 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.029 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.029 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.045 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.045 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.045 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.045 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.045 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.045 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.045 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.046 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.046 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.046 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.046 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.046 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.046 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.046 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.046 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.065 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.065 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.065 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.065 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.065 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.065 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.065 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.065 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.065 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.067 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.067 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.067 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.067 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.068 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.069 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.087 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.087 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.087 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.087 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.087 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.087 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.087 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.138 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.138 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.138 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.138 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.138 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.138 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.138 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.138 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.141 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.142 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.143 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.143 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.143 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.143 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.143 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.143 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.143 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.144 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.145 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.145 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.145 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.145 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.145 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.145 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.145 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.145 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.145 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.145 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.145 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.146 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.153 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.153 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.153 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.153 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.153 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.153 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.153 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.200 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.200 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.200 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.200 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.200 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.200 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.200 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.200 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.203 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.203 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.203 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.203 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.203 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.203 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.203 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.206 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.207 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.207 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.207 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.207 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.207 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.207 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.207 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.207 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.207 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.207 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.207 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.208 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.208 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.208 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.208 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.208 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.208 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.208 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.211 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.211 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.211 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.211 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.211 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.211 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.211 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.211 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.215 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.215 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.215 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.215 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.215 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.215 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.215 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.230 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.230 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.230 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.230 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.230 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.230 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.230 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.230 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.231 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.232 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.232 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.232 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.232 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.232 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.232 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.232 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.235 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.235 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.235 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.235 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.235 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.235 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.235 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.235 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.235 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.235 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.235 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.235 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.235 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.239 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.240 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.240 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.241 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.241 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.241 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.241 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.241 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.241 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.241 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.241 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.242 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.242 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.242 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.242 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.242 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.242 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.242 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.245 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.245 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.246 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.246 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.246 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: indexable
2025-05-27 11:21:08.246 [com.actonia.entity.HtmlChangeExample.main()] WARN  c.a.e.FieldExtractor - No extractor found for previous field: noodp
2025-05-27 11:21:08.246 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChange - url: https://example.com/page, htmlChangeList size: 10
2025-05-27 11:21:08.246 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Processed 1000 iterations in 500 ms
2025-05-27 11:21:08.246 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Average time per iteration: 0.5 ms
2025-05-27 11:21:08.246 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - Iterations per second: 2000
2025-05-27 11:21:08.246 [com.actonia.entity.HtmlChangeExample.main()] INFO  c.a.e.HtmlChangeExample - 
2025-05-27 11:39:41.499 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 5
2025-05-27 11:39:41.502 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.502 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.503 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.503 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.503 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.503 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.504 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.504 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.504 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.504 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.505 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.505 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.508 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.508 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.508 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.509 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.509 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.509 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.511 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.515 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.516 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.516 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.517 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.517 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.518 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.518 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.519 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.519 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.519 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.519 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.519 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.520 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.520 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.520 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.520 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.520 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.521 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.521 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.521 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.521 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.522 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.522 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.522 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.522 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.522 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.522 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.523 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.523 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.523 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.523 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.523 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.524 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.524 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.524 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.524 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.524 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.524 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.525 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.525 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.525 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.525 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.525 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.525 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.526 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.526 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.526 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.526 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.527 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.527 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.527 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.527 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.528 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.528 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.528 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.528 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.528 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.528 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.529 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.529 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.529 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.529 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.529 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.529 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.530 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.530 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.530 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.530 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.530 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.530 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.531 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.532 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.532 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.532 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.532 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.532 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.533 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.533 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.533 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.533 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.533 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 8
2025-05-27 11:39:41.533 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 0
2025-05-27 11:39:41.534 [main] INFO  c.a.e.HtmlChange - url: https://example.com, htmlChangeList size: 7
2025-05-27 11:39:41.535 [main] WARN  c.a.e.UnifiedFieldExtractor - No extractor found for field: nonExistentField
