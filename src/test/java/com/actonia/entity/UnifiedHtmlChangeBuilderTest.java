package com.actonia.entity;

import com.actonia.value.object.CrawlerResponse;
import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Test class for the unified HtmlChangeBuilder that compares two entities of the same type
 */
public class UnifiedHtmlChangeBuilderTest {
    
    private HtmlClickHouseEntity previousEntity;
    private HtmlClickHouseEntity currentEntity;
    
    @Before
    public void setUp() {
        // Create previous entity
        previousEntity = new HtmlClickHouseEntity();
        previousEntity.setUrl("https://example.com");
        previousEntity.setTrackDate(new Date());
        previousEntity.setDomainId(123);
        previousEntity.setCrawlTimestamp(new Date(System.currentTimeMillis() - 86400000)); // 1 day ago
        
        // Create crawler response for previous entity
        CrawlerResponse previousResponse = new CrawlerResponse();
        previousResponse.setTitle("Old Title");
        previousResponse.setDescription("Old Description");
        previousResponse.setH1(new String[]{"Old H1"});
        previousResponse.setH2(new String[]{"Old H2"});
        previousResponse.setContent_type("text/html");
        previousResponse.setFollow_flg("1");
        previousResponse.setCanonical("https://example.com/old-canonical");
        previousResponse.setIndexable(false);
        previousResponse.setResponse_code("200");
        
        previousEntity.setCrawlerResponse(previousResponse);
        
        // Create current entity
        currentEntity = new HtmlClickHouseEntity();
        currentEntity.setUrl("https://example.com");
        currentEntity.setTrackDate(new Date());
        currentEntity.setDomainId(123);
        currentEntity.setCrawlTimestamp(new Date());
        
        // Create crawler response for current entity
        CrawlerResponse currentResponse = new CrawlerResponse();
        currentResponse.setTitle("New Title");
        currentResponse.setDescription("New Description");
        currentResponse.setH1(new String[]{"New H1"});
        currentResponse.setH2(new String[]{"New H2"});
        currentResponse.setContent_type("text/html; charset=utf-8");
        currentResponse.setFollow_flg("0");
        currentResponse.setCanonical("https://example.com/new-canonical");
        currentResponse.setIndexable(true);
        currentResponse.setResponse_code("200");
        
        currentEntity.setCrawlerResponse(currentResponse);
    }
    
    @Test
    public void testBuildFromEntitiesWithJsonMapping() throws Exception {
        String fieldMappingJson = "{\n" +
            "  \"title\": 89,\n" +
            "  \"description\": 17,\n" +
            "  \"h1\": 29,\n" +
            "  \"h2\": 34,\n" +
            "  \"canonical\": 39,\n" +
            "  \"followFlg\": 52,\n" +
            "  \"indexable\": 48\n" +
            "}";
        
        List<HtmlChange> changes = HtmlChangeBuilder.buildFromEntities(previousEntity, currentEntity, fieldMappingJson);
        
        assertNotNull(changes);
        // Should only include changes where values are different
        assertTrue("Should have changes", changes.size() > 0);
        
        // Verify each change has correct properties
        for (HtmlChange change : changes) {
            assertEquals("https://example.com", change.getUrl());
            assertTrue("Domain ID should be 123", change.getDomainId() == 123);
            assertNotNull(change.getTrackDate());
            assertTrue("Change ID should be positive", change.getChgId() > 0);
            assertNotNull("Previous value should not be null", change.getPrevValue());
            assertNotNull("Current value should not be null", change.getCurrValue());
            assertFalse("Previous and current values should be different", 
                change.getPrevValue().equals(change.getCurrValue()));
        }
    }
    
    @Test
    public void testBuildFromMapEntities() throws Exception {
        Map<String, Integer> fieldMappings = new HashMap<>();
        fieldMappings.put("title", 89);
        fieldMappings.put("description", 17);
        fieldMappings.put("canonical", 39);
        fieldMappings.put("followFlg", 52);
        fieldMappings.put("indexable", 48);
        
        List<HtmlChange> changes = HtmlChangeBuilder.buildFromMapEntities(previousEntity, currentEntity, fieldMappings);
        
        assertNotNull(changes);
        assertTrue("Should have changes", changes.size() > 0);
        
        // Verify specific changes
        boolean foundTitleChange = false;
        boolean foundCanonicalChange = false;
        boolean foundIndexableChange = false;
        
        for (HtmlChange change : changes) {
            switch (change.getChgId()) {
                case 89: // Title
                    foundTitleChange = true;
                    assertEquals("Old Title", change.getPrevValue());
                    assertEquals("New Title", change.getCurrValue());
                    break;
                case 39: // Canonical
                    foundCanonicalChange = true;
                    assertEquals("https://example.com/old-canonical", change.getPrevValue());
                    assertEquals("https://example.com/new-canonical", change.getCurrValue());
                    break;
                case 48: // Indexable
                    foundIndexableChange = true;
                    assertEquals("false", change.getPrevValue());
                    assertEquals("true", change.getCurrValue());
                    break;
            }
        }
        
        assertTrue("Should find title change", foundTitleChange);
        assertTrue("Should find canonical change", foundCanonicalChange);
        assertTrue("Should find indexable change", foundIndexableChange);
    }
    
    @Test
    public void testNoChangesWhenValuesAreSame() throws Exception {
        // Create two identical entities
        HtmlClickHouseEntity entity1 = new HtmlClickHouseEntity();
        entity1.setUrl("https://example.com");
        entity1.setTrackDate(new Date());
        entity1.setDomainId(123);
        entity1.setCrawlTimestamp(new Date());
        
        CrawlerResponse response1 = new CrawlerResponse();
        response1.setTitle("Same Title");
        response1.setDescription("Same Description");
        response1.setCanonical("https://example.com/same");
        entity1.setCrawlerResponse(response1);
        
        HtmlClickHouseEntity entity2 = new HtmlClickHouseEntity();
        entity2.setUrl("https://example.com");
        entity2.setTrackDate(new Date());
        entity2.setDomainId(123);
        entity2.setCrawlTimestamp(new Date());
        
        CrawlerResponse response2 = new CrawlerResponse();
        response2.setTitle("Same Title");
        response2.setDescription("Same Description");
        response2.setCanonical("https://example.com/same");
        entity2.setCrawlerResponse(response2);
        
        String fieldMappingJson = "{\n" +
            "  \"title\": 89,\n" +
            "  \"description\": 17,\n" +
            "  \"canonical\": 39\n" +
            "}";
        
        List<HtmlChange> changes = HtmlChangeBuilder.buildFromEntities(entity1, entity2, fieldMappingJson);
        
        assertNotNull(changes);
        assertEquals("Should have no changes when values are the same", 0, changes.size());
    }
    
    @Test
    public void testUnifiedFieldExtractor() {
        UnifiedFieldExtractor extractor = new UnifiedFieldExtractor();
        
        // Test title extraction
        String title = extractor.extractValue(currentEntity, "title");
        assertEquals("New Title", title);
        
        // Test description extraction
        String description = extractor.extractValue(currentEntity, "description");
        assertEquals("New Description", description);
        
        // Test canonical extraction
        String canonical = extractor.extractValue(currentEntity, "canonical");
        assertEquals("https://example.com/new-canonical", canonical);
        
        // Test boolean field
        String indexable = extractor.extractValue(currentEntity, "indexable");
        assertEquals("true", indexable);
        
        // Test array field
        String h1 = extractor.extractValue(currentEntity, "h1");
        assertTrue("H1 should contain JSON array", h1.contains("New H1"));
        
        // Test entity field
        String url = extractor.extractValue(currentEntity, "url");
        assertEquals("https://example.com", url);
        
        // Test non-existent field
        String nonExistent = extractor.extractValue(currentEntity, "nonExistentField");
        assertNull("Non-existent field should return null", nonExistent);
    }
    
    @Test
    public void testFieldNameVariations() {
        UnifiedFieldExtractor extractor = new UnifiedFieldExtractor();
        
        // Test both camelCase and snake_case variations
        String contentType1 = extractor.extractValue(currentEntity, "contentType");
        String contentType2 = extractor.extractValue(currentEntity, "content_type");
        
        assertNotNull("camelCase should work", contentType1);
        assertNotNull("snake_case should work", contentType2);
        assertEquals("Both variations should return same value", contentType1, contentType2);
        
        String followFlg1 = extractor.extractValue(currentEntity, "followFlg");
        String followFlg2 = extractor.extractValue(currentEntity, "follow_flg");
        
        assertNotNull("camelCase should work", followFlg1);
        assertNotNull("snake_case should work", followFlg2);
        assertEquals("Both variations should return same value", followFlg1, followFlg2);
    }
    
    @Test
    public void testPerformanceWithManyFields() throws Exception {
        Map<String, Integer> manyFields = new HashMap<>();
        manyFields.put("title", 89);
        manyFields.put("description", 17);
        manyFields.put("h1", 29);
        manyFields.put("h2", 34);
        manyFields.put("canonical", 39);
        manyFields.put("followFlg", 52);
        manyFields.put("indexable", 48);
        manyFields.put("contentType", 45);
        manyFields.put("responseCode", 80);
        manyFields.put("url", 1);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < 100; i++) {
            List<HtmlChange> changes = HtmlChangeBuilder.buildFromMapEntities(previousEntity, currentEntity, manyFields);
            assertNotNull(changes);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        assertTrue("100 iterations should complete in reasonable time (< 1000ms)", duration < 1000);
        System.out.println("100 iterations completed in " + duration + "ms");
    }
}
