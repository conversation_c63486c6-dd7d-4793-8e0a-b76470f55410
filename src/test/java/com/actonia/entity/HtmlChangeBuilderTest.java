package com.actonia.entity;

import com.actonia.value.object.CrawlerResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Test class for the simplified HtmlChangeBuilder
 */
public class HtmlChangeBuilderTest {
    
    private UrlMetricsEntityV3 previousEntity;
    private HtmlClickHouseEntity currentEntity;
    
    @BeforeEach
    public void setUp() {
        // Create mock previous entity
        previousEntity = new UrlMetricsEntityV3();
        previousEntity.setUrl("https://example.com");
        previousEntity.setTitle("Old Title");
        previousEntity.setDescription("Old Description");
        previousEntity.setH1_array(new String[]{"Old H1"});
        previousEntity.setH2_array(new String[]{"Old H2"});
        previousEntity.setContent_type("text/html");
        previousEntity.setFollow_flg("1");
        previousEntity.setResponse_code("200");
        previousEntity.setCrawl_timestamp("2024-01-01 10:00:00");
        previousEntity.setUrlType(1);
        
        // Create mock current entity
        currentEntity = new HtmlClickHouseEntity();
        currentEntity.setUrl("https://example.com");
        currentEntity.setTrackDate(new Date());
        currentEntity.setDomainId(123);
        currentEntity.setCrawlTimestamp(new Date());
        
        // Create crawler response for current entity
        CrawlerResponse crawlerResponse = new CrawlerResponse();
        crawlerResponse.setTitle("New Title");
        crawlerResponse.setDescription("New Description");
        crawlerResponse.setH1(new String[]{"New H1"});
        crawlerResponse.setH2(new String[]{"New H2"});
        crawlerResponse.setContent_type("text/html; charset=utf-8");
        crawlerResponse.setFollow_flg("0");
        crawlerResponse.setResponse_code("200");
        
        currentEntity.setCrawlerResponse(crawlerResponse);
    }
    
    @Test
    public void testBuildFromJsonMapping() throws Exception {
        String fieldMappingJson = """
        {
          "title": 89,
          "description": 17,
          "h1": 29,
          "h2": 34
        }
        """;
        
        List<HtmlChange> changes = HtmlChangeBuilder.buildFromJson(previousEntity, currentEntity, fieldMappingJson);
        
        assertNotNull(changes);
        assertEquals(4, changes.size());
        
        // Verify each change has correct change ID and values
        for (HtmlChange change : changes) {
            assertNotNull(change.getUrl());
            assertNotNull(change.getTrackDate());
            assertNotNull(change.getDomainId());
            assertTrue(change.getChgId() > 0);
            
            switch (change.getChgId()) {
                case 89: // Title
                    assertEquals("Old Title", change.getPrevValue());
                    assertEquals("New Title", change.getCurrValue());
                    break;
                case 17: // Description
                    assertEquals("Old Description", change.getPrevValue());
                    assertEquals("New Description", change.getCurrValue());
                    break;
                case 29: // H1
                    assertTrue(change.getPrevValue().contains("Old H1"));
                    assertTrue(change.getCurrValue().contains("New H1"));
                    break;
                case 34: // H2
                    assertTrue(change.getPrevValue().contains("Old H2"));
                    assertTrue(change.getCurrValue().contains("New H2"));
                    break;
            }
        }
    }
    
    @Test
    public void testBuildFromMapMapping() throws Exception {
        Map<String, Integer> fieldMappings = HtmlChangeBuilder.createSampleFieldMappings();
        
        List<HtmlChange> changes = HtmlChangeBuilder.buildFromMap(previousEntity, currentEntity, fieldMappings);
        
        assertNotNull(changes);
        assertTrue(changes.size() > 0);
        
        // Verify common properties
        for (HtmlChange change : changes) {
            assertEquals("https://example.com", change.getUrl());
            assertEquals(Integer.valueOf(123), change.getDomainId());
            assertNotNull(change.getTrackDate());
        }
    }
    
    @Test
    public void testSampleFieldMappings() {
        Map<String, Integer> mappings = HtmlChangeBuilder.createSampleFieldMappings();
        
        assertNotNull(mappings);
        assertTrue(mappings.size() > 0);
        assertTrue(mappings.containsKey("title"));
        assertTrue(mappings.containsKey("description"));
        assertTrue(mappings.containsKey("h1"));
        assertTrue(mappings.containsKey("h2"));
        
        assertEquals(Integer.valueOf(89), mappings.get("title"));
        assertEquals(Integer.valueOf(17), mappings.get("description"));
    }
    
    @Test
    public void testEmptyJsonMapping() throws Exception {
        String emptyJson = "{}";
        
        List<HtmlChange> changes = HtmlChangeBuilder.buildFromJson(previousEntity, currentEntity, emptyJson);
        
        assertNotNull(changes);
        assertEquals(0, changes.size());
    }
}
