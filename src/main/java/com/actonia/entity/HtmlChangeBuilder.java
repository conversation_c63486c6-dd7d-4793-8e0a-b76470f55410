package com.actonia.entity;

import com.google.gson.Gson;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Simplified HTML Change Builder that uses JSON configuration to generate HtmlChange objects
 * 
 * Usage example:
 * 
 * String fieldMappingJson = """
 * {
 *   "title": 89,
 *   "description": 17,
 *   "h1": 29,
 *   "h2": 34,
 *   "contentType": 45,
 *   "followFlg": 52
 * }
 * """;
 * 
 * List<HtmlChange> changes = HtmlChangeBuilder.buildFromJson(previous, current, fieldMappingJson);
 */
public class HtmlChangeBuilder {
    
    private static final Logger log = LogManager.getLogger(HtmlChangeBuilder.class);
    private static final Gson gson = new Gson();
    
    /**
     * Build HtmlChange objects from JSON field mapping
     * @param previous Previous entity state
     * @param current Current entity state  
     * @param fieldMappingJson JSON string mapping field names to change IDs
     * @return List of HtmlChange objects
     */
    public static List<HtmlChange> buildFromJson(UrlMetricsEntityV3 previous, HtmlClickHouseEntity current, String fieldMappingJson) throws Exception {
        return HtmlChange.buildHtmlChangeFromJson(previous, current, fieldMappingJson);
    }
    
    /**
     * Build HtmlChange objects from Map field mapping
     * @param previous Previous entity state
     * @param current Current entity state  
     * @param fieldMappings Map of field names to change IDs
     * @return List of HtmlChange objects
     */
    public static List<HtmlChange> buildFromMap(UrlMetricsEntityV3 previous, HtmlClickHouseEntity current, Map<String, Integer> fieldMappings) throws Exception {
        String fieldMappingJson = gson.toJson(fieldMappings);
        return HtmlChange.buildHtmlChangeFromJson(previous, current, fieldMappingJson);
    }
    
    /**
     * Create a sample field mapping for common HTML change indicators
     * @return Map of common field mappings
     */
    public static Map<String, Integer> createSampleFieldMappings() {
        Map<String, Integer> mappings = new HashMap<>();
        
        // Common HTML elements
        mappings.put("title", 89);           // TITLE_CHG_IND
        mappings.put("description", 17);     // DESCRIPTION_CHG_IND  
        mappings.put("h1", 29);             // H1_CHG_IND
        mappings.put("h2", 34);             // H2_CHG_IND
        
        // Meta tags
        mappings.put("contentType", 45);     // CONTENT_TYPE_CHG_IND
        mappings.put("followFlg", 52);       // FOLLOW_FLG_CHG_IND
        mappings.put("indexable", 48);       // INDEXABLE_CHG_IND
        
        // URLs and redirects
        mappings.put("canonical", 39);       // CANONICAL_CHG_IND
        mappings.put("amphtmlHref", 2);      // AMPHTML_HREF_CHG_IND
        
        // Response related
        mappings.put("responseCode", 80);    // RESPONSE_CODE_CHG_IND
        
        return mappings;
    }
    
    /**
     * Example usage method
     */
    public static void exampleUsage(UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
        try {
            // Method 1: Using JSON string
            String fieldMappingJson = """
            {
              "title": 89,
              "description": 17,
              "h1": 29,
              "h2": 34,
              "contentType": 45,
              "followFlg": 52
            }
            """;
            
            List<HtmlChange> changes1 = buildFromJson(previous, current, fieldMappingJson);
            log.info("Generated {} changes using JSON mapping", changes1.size());
            
            // Method 2: Using Map
            Map<String, Integer> fieldMappings = createSampleFieldMappings();
            List<HtmlChange> changes2 = buildFromMap(previous, current, fieldMappings);
            log.info("Generated {} changes using Map mapping", changes2.size());
            
            // Method 3: Custom mapping for specific fields
            Map<String, Integer> customMappings = new HashMap<>();
            customMappings.put("title", 89);
            customMappings.put("description", 17);
            
            List<HtmlChange> changes3 = buildFromMap(previous, current, customMappings);
            log.info("Generated {} changes using custom mapping", changes3.size());
            
        } catch (Exception e) {
            log.error("Error in example usage: {}", e.getMessage(), e);
        }
    }
}
