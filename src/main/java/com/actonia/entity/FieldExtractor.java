package com.actonia.entity;

import com.actonia.value.object.CrawlerResponse;
import com.google.gson.Gson;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * High-performance field extractor that uses direct method calls instead of reflection
 * This provides much better performance compared to reflection-based approaches
 */
public class FieldExtractor {

    private static final Logger log = LogManager.getLogger(FieldExtractor.class);
    private static final Gson gson = new Gson();

    // Function mappings for previous entity (UrlMetricsEntityV3)
    private final Map<String, Function<UrlMetricsEntityV3, String>> previousExtractors;

    // Function mappings for current entity (HtmlClickHouseEntity)
    private final Map<String, Function<HtmlClickHouseEntity, String>> currentExtractors;

    public FieldExtractor() {
        this.previousExtractors = initializePreviousExtractors();
        this.currentExtractors = initializeCurrentExtractors();
    }

    /**
     * Get value from previous entity (UrlMetricsEntityV3)
     */
    public String getPreviousValue(UrlMetricsEntityV3 entity, String fieldName) {
        Function<UrlMetricsEntityV3, String> extractor = previousExtractors.get(fieldName);
        if (extractor != null) {
            try {
                return extractor.apply(entity);
            } catch (Exception e) {
                log.warn("Error extracting field '{}' from previous entity: {}", fieldName, e.getMessage());
                return null;
            }
        }
        log.warn("No extractor found for previous field: {}", fieldName);
        return null;
    }

    /**
     * Get value from current entity (HtmlClickHouseEntity)
     */
    public String getCurrentValue(HtmlClickHouseEntity entity, String fieldName) {
        Function<HtmlClickHouseEntity, String> extractor = currentExtractors.get(fieldName);
        if (extractor != null) {
            try {
                return extractor.apply(entity);
            } catch (Exception e) {
                log.warn("Error extracting field '{}' from current entity: {}", fieldName, e.getMessage());
                return null;
            }
        }
        log.warn("No extractor found for current field: {}", fieldName);
        return null;
    }

    /**
     * Initialize extractors for previous entity (UrlMetricsEntityV3)
     */
    private Map<String, Function<UrlMetricsEntityV3, String>> initializePreviousExtractors() {
        Map<String, Function<UrlMetricsEntityV3, String>> extractors = new HashMap<>();

        // Basic fields
        extractors.put("title", entity -> entity.getTitle());
        extractors.put("description", entity -> entity.getDescription());
        extractors.put("contentType", entity -> entity.getContent_type());
        extractors.put("content_type", entity -> entity.getContent_type());
        extractors.put("followFlg", entity -> entity.getFollow_flg());
        extractors.put("follow_flg", entity -> entity.getFollow_flg());
        extractors.put("canonical", entity -> entity.getCanonical());
        extractors.put("amphtmlHref", entity -> entity.getAmphtml_href());
        extractors.put("amphtml_href", entity -> entity.getAmphtml_href());
        extractors.put("analyzedUrlS", entity -> entity.getAnalyzed_url_s());
        extractors.put("analyzed_url_s", entity -> entity.getAnalyzed_url_s());
        extractors.put("archiveFlg", entity -> entity.getArchive_flg());
        extractors.put("archive_flg", entity -> entity.getArchive_flg());
        extractors.put("baseTag", entity -> entity.getBase_tag());
        extractors.put("base_tag", entity -> entity.getBase_tag());
        extractors.put("baseTagTarget", entity -> entity.getBase_tag_target());
        extractors.put("base_tag_target", entity -> entity.getBase_tag_target());
        extractors.put("viewportContent", entity -> entity.getViewportContent());
        extractors.put("responseCode", entity -> entity.getResponse_code());
        extractors.put("response_code", entity -> entity.getResponse_code());

        // Array fields - convert to JSON
        extractors.put("h1", entity -> convertArrayToJson(entity.getH1_array()));
        extractors.put("h1_array", entity -> convertArrayToJson(entity.getH1_array()));
        extractors.put("h2", entity -> convertArrayToJson(entity.getH2_array()));
        extractors.put("h2_array", entity -> convertArrayToJson(entity.getH2_array()));
        extractors.put("customData", entity -> convertArrayToJson(entity.getCustom_data()));
        extractors.put("custom_data", entity -> convertArrayToJson(entity.getCustom_data()));

        // Boolean fields
        extractors.put("amphtmlFlag", entity -> convertBooleanToString(entity.getAmphtml_flag()));
        extractors.put("amphtml_flag", entity -> convertBooleanToString(entity.getAmphtml_flag()));
        extractors.put("baseTagFlag", entity -> convertBooleanToString(entity.getBase_tag_flag()));
        extractors.put("base_tag_flag", entity -> convertBooleanToString(entity.getBase_tag_flag()));
        extractors.put("canonicalHeaderFlag", entity -> convertBooleanToString(entity.getCanonical_header_flag()));
        extractors.put("canonical_header_flag", entity -> convertBooleanToString(entity.getCanonical_header_flag()));

        // Integer fields
        extractors.put("descriptionLength", entity -> convertIntegerToString(entity.getDescription_length()));
        extractors.put("description_length", entity -> convertIntegerToString(entity.getDescription_length()));
        extractors.put("h1Count", entity -> convertIntegerToString(entity.getH1_count()));
        extractors.put("h1_count", entity -> convertIntegerToString(entity.getH1_count()));
        extractors.put("h1Length", entity -> convertIntegerToString(entity.getH1_length()));
        extractors.put("h1_length", entity -> convertIntegerToString(entity.getH1_length()));
        extractors.put("finalResponseCode", entity -> convertIntegerToString(entity.getFinal_response_code()));
        extractors.put("final_response_code", entity -> convertIntegerToString(entity.getFinal_response_code()));
        extractors.put("titleLength", entity -> convertIntegerToString(entity.getTitle_length()));
        extractors.put("title_length", entity -> convertIntegerToString(entity.getTitle_length()));
        extractors.put("redirectTimes", entity -> convertIntegerToString(entity.getRedirect_times()));
        extractors.put("redirect_times", entity -> convertIntegerToString(entity.getRedirect_times()));

        // Additional string fields
        extractors.put("canonicalType", entity -> entity.getCanonical_type());
        extractors.put("canonical_type", entity -> entity.getCanonical_type());
        extractors.put("canonicalHeaderType", entity -> entity.getCanonical_header_type());
        extractors.put("canonical_header_type", entity -> entity.getCanonical_header_type());
        extractors.put("errorMessage", entity -> entity.getError_message());
        extractors.put("error_message", entity -> entity.getError_message());
        extractors.put("redirectFinalUrl", entity -> entity.getRedirectFinalUrl());
        extractors.put("redirectFinalUrl", entity -> entity.getRedirectFinalUrl());
        extractors.put("blockedByRobots", entity -> entity.getBlocked_by_robots());
        extractors.put("blocked_by_robots", entity -> entity.getBlocked_by_robots());

        // Additional boolean fields
        extractors.put("canonicalFlg", entity -> entity.getCanonical_flg());
        extractors.put("canonical_flg", entity -> entity.getCanonical_flg());
        extractors.put("descriptionFlg", entity -> entity.getDescription_flg());
        extractors.put("description_flg", entity -> entity.getDescription_flg());
        extractors.put("h1Flg", entity -> entity.getH1_flg());
        extractors.put("h1_flg", entity -> entity.getH1_flg());
        extractors.put("indexFlg", entity -> entity.getIndex_flg());
        extractors.put("index_flg", entity -> entity.getIndex_flg());
        extractors.put("titleFlg", entity -> entity.getTitle_flg());
        extractors.put("title_flg", entity -> entity.getTitle_flg());

        return extractors;
    }

    /**
     * Initialize extractors for current entity (HtmlClickHouseEntity)
     */
    private Map<String, Function<HtmlClickHouseEntity, String>> initializeCurrentExtractors() {
        Map<String, Function<HtmlClickHouseEntity, String>> extractors = new HashMap<>();

        // Fields from CrawlerResponse
        extractors.put("title", entity -> getCrawlerResponseField(entity, cr -> cr.getTitle()));
        extractors.put("description", entity -> getCrawlerResponseField(entity, cr -> cr.getDescription()));
        extractors.put("contentType", entity -> getCrawlerResponseField(entity, cr -> cr.getContent_type()));
        extractors.put("content_type", entity -> getCrawlerResponseField(entity, cr -> cr.getContent_type()));
        extractors.put("followFlg", entity -> getCrawlerResponseField(entity, cr -> cr.getFollow_flg()));
        extractors.put("follow_flg", entity -> getCrawlerResponseField(entity, cr -> cr.getFollow_flg()));
        extractors.put("canonical", entity -> getCrawlerResponseField(entity, cr -> cr.getCanonical()));
        extractors.put("amphtmlHref", entity -> getCrawlerResponseField(entity, cr -> cr.getAmphtml_href()));
        extractors.put("amphtml_href", entity -> getCrawlerResponseField(entity, cr -> cr.getAmphtml_href()));
        extractors.put("analyzedUrlS", entity -> getCrawlerResponseField(entity, cr -> cr.getAnalyzed_url_s()));
        extractors.put("analyzed_url_s", entity -> getCrawlerResponseField(entity, cr -> cr.getAnalyzed_url_s()));
        extractors.put("archiveFlg", entity -> getCrawlerResponseField(entity, cr -> cr.getArchive_flg()));
        extractors.put("archive_flg", entity -> getCrawlerResponseField(entity, cr -> cr.getArchive_flg()));
        extractors.put("baseTag", entity -> getCrawlerResponseField(entity, cr -> cr.getBase_tag()));
        extractors.put("base_tag", entity -> getCrawlerResponseField(entity, cr -> cr.getBase_tag()));
        extractors.put("baseTagTarget", entity -> getCrawlerResponseField(entity, cr -> cr.getBase_tag_target()));
        extractors.put("base_tag_target", entity -> getCrawlerResponseField(entity, cr -> cr.getBase_tag_target()));
        extractors.put("viewportContent", entity -> getCrawlerResponseField(entity, cr -> cr.getViewport_content()));
        extractors.put("viewport_content", entity -> getCrawlerResponseField(entity, cr -> cr.getViewport_content()));
        extractors.put("responseCode", entity -> getCrawlerResponseField(entity, cr -> cr.getResponse_code()));
        extractors.put("response_code", entity -> getCrawlerResponseField(entity, cr -> cr.getResponse_code()));

        // Array fields from CrawlerResponse
        extractors.put("h1", entity -> getCrawlerResponseField(entity, cr -> convertArrayToJson(cr.getH1())));
        extractors.put("h1_array", entity -> getCrawlerResponseField(entity, cr -> convertArrayToJson(cr.getH1())));
        extractors.put("h2", entity -> getCrawlerResponseField(entity, cr -> convertArrayToJson(cr.getH2())));
        extractors.put("h2_array", entity -> getCrawlerResponseField(entity, cr -> convertArrayToJson(cr.getH2())));
        extractors.put("customData", entity -> getCrawlerResponseField(entity, cr -> convertArrayToJson(cr.getCustom_data())));
        extractors.put("custom_data", entity -> getCrawlerResponseField(entity, cr -> convertArrayToJson(cr.getCustom_data())));

        // Boolean fields from CrawlerResponse
        extractors.put("amphtmlFlag", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getAmphtml_flag())));
        extractors.put("amphtml_flag", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getAmphtml_flag())));
        extractors.put("baseTagFlag", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getBase_tag_flag())));
        extractors.put("base_tag_flag", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getBase_tag_flag())));
        extractors.put("indexable", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getIndexable())));
        extractors.put("noodp", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getNoodp())));
        extractors.put("nosnippet", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getNosnippet())));
        extractors.put("noydir", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getNoydir())));

        // Integer fields from CrawlerResponse
        extractors.put("descriptionLength", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getDescription_length())));
        extractors.put("description_length", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getDescription_length())));
        extractors.put("h1Count", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getH1_count())));
        extractors.put("h1_count", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getH1_count())));
        extractors.put("h1Length", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getH1_length())));
        extractors.put("h1_length", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getH1_length())));
        extractors.put("outlinkCount", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getOutlink_count())));
        extractors.put("outlink_count", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getOutlink_count())));
        extractors.put("titleLength", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getTitle_length())));
        extractors.put("title_length", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getTitle_length())));
        extractors.put("redirectTimes", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getRedirect_times())));
        extractors.put("redirect_times", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getRedirect_times())));
        extractors.put("finalResponseCode", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getFinal_response_code())));
        extractors.put("final_response_code", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getFinal_response_code())));

        // Additional string fields from CrawlerResponse
        extractors.put("canonicalType", entity -> getCrawlerResponseField(entity, cr -> cr.getCanonical_type()));
        extractors.put("canonical_type", entity -> getCrawlerResponseField(entity, cr -> cr.getCanonical_type()));
        extractors.put("canonicalHeaderType", entity -> getCrawlerResponseField(entity, cr -> cr.getCanonical_header_type()));
        extractors.put("canonical_header_type", entity -> getCrawlerResponseField(entity, cr -> cr.getCanonical_header_type()));
        extractors.put("errorMessage", entity -> getCrawlerResponseField(entity, cr -> cr.getError_message()));
        extractors.put("error_message", entity -> getCrawlerResponseField(entity, cr -> cr.getError_message()));
        extractors.put("redirectFinalUrl", entity -> getCrawlerResponseField(entity, cr -> cr.getRedirect_final_url()));
        extractors.put("redirect_final_url", entity -> getCrawlerResponseField(entity, cr -> cr.getRedirect_final_url()));
        extractors.put("blockedByRobots", entity -> getCrawlerResponseField(entity, cr -> cr.getBlocked_by_robots()));
        extractors.put("blocked_by_robots", entity -> getCrawlerResponseField(entity, cr -> cr.getBlocked_by_robots()));

        // Additional boolean fields from CrawlerResponse
        extractors.put("canonicalFlg", entity -> getCrawlerResponseField(entity, cr -> cr.getCanonical_flg()));
        extractors.put("canonical_flg", entity -> getCrawlerResponseField(entity, cr -> cr.getCanonical_flg()));
        extractors.put("descriptionFlg", entity -> getCrawlerResponseField(entity, cr -> cr.getDescription_flg()));
        extractors.put("description_flg", entity -> getCrawlerResponseField(entity, cr -> cr.getDescription_flg()));
        extractors.put("h1Flg", entity -> getCrawlerResponseField(entity, cr -> cr.getH1_flg()));
        extractors.put("h1_flg", entity -> getCrawlerResponseField(entity, cr -> cr.getH1_flg()));
        extractors.put("indexFlg", entity -> getCrawlerResponseField(entity, cr -> cr.getIndex_flg()));
        extractors.put("index_flg", entity -> getCrawlerResponseField(entity, cr -> cr.getIndex_flg()));
        extractors.put("titleFlg", entity -> getCrawlerResponseField(entity, cr -> cr.getTitle_flg()));
        extractors.put("title_flg", entity -> getCrawlerResponseField(entity, cr -> cr.getTitle_flg()));
        extractors.put("canonicalHeaderFlag", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getCanonical_header_flag())));
        extractors.put("canonical_header_flag", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getCanonical_header_flag())));

        return extractors;
    }

    /**
     * Helper method to safely extract field from CrawlerResponse
     */
    private String getCrawlerResponseField(HtmlClickHouseEntity entity, Function<CrawlerResponse, String> extractor) {
        if (entity == null || entity.getCrawlerResponse() == null) {
            return null;
        }
        return extractor.apply(entity.getCrawlerResponse());
    }

    /**
     * Convert array to JSON string
     */
    private String convertArrayToJson(Object[] array) {
        if (array == null || array.length == 0) {
            return null;
        }
        return gson.toJson(array);
    }

    /**
     * Convert Boolean to String
     */
    private String convertBooleanToString(Boolean value) {
        if (value == null) {
            return null;
        }
        return value.toString();
    }

    /**
     * Convert Integer to String
     */
    private String convertIntegerToString(Integer value) {
        if (value == null) {
            return null;
        }
        return value.toString();
    }
}
