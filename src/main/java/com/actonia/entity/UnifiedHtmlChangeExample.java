package com.actonia.entity;

import com.actonia.value.object.CrawlerResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Example demonstrating the unified HTML Change Builder for comparing two entities of the same type
 */
public class UnifiedHtmlChangeExample {
    
    private static final Logger log = LogManager.getLogger(UnifiedHtmlChangeExample.class);
    
    public static void main(String[] args) {
        try {
            // Create sample entities
            HtmlClickHouseEntity previousEntity = createPreviousEntity();
            HtmlClickHouseEntity currentEntity = createCurrentEntity();
            
            // Example 1: Basic entity comparison with JSON mapping
            basicEntityComparisonExample(previousEntity, currentEntity);
            
            // Example 2: Comprehensive field mapping
            comprehensiveEntityExample(previousEntity, currentEntity);
            
            // Example 3: SEO-focused comparison
            seoFocusedExample(previousEntity, currentEntity);
            
            // Example 4: Performance demonstration
            performanceExample(previousEntity, currentEntity);
            
        } catch (Exception e) {
            log.error("Error in unified example: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Example 1: Basic entity comparison with JSON mapping
     */
    public static void basicEntityComparisonExample(HtmlClickHouseEntity previous, HtmlClickHouseEntity current) throws Exception {
        log.info("=== Example 1: Basic Entity Comparison ===");
        
        String fieldMappingJson = "{\n" +
            "  \"title\": 89,\n" +
            "  \"description\": 17,\n" +
            "  \"h1\": 29,\n" +
            "  \"h2\": 34,\n" +
            "  \"canonical\": 39,\n" +
            "  \"followFlg\": 52\n" +
            "}";
        
        List<HtmlChange> changes = HtmlChangeBuilder.buildFromEntities(previous, current, fieldMappingJson);
        
        log.info("Generated {} changes between entities:", changes.size());
        for (HtmlChange change : changes) {
            log.info("Change ID: {}, Field changed from '{}' to '{}'", 
                change.getChgId(), 
                truncate(change.getPrevValue(), 50), 
                truncate(change.getCurrValue(), 50));
        }
        log.info("");
    }
    
    /**
     * Example 2: Comprehensive field mapping using Map
     */
    public static void comprehensiveEntityExample(HtmlClickHouseEntity previous, HtmlClickHouseEntity current) throws Exception {
        log.info("=== Example 2: Comprehensive Entity Comparison ===");
        
        Map<String, Integer> fieldMappings = new HashMap<>();
        fieldMappings.put("title", 89);
        fieldMappings.put("description", 17);
        fieldMappings.put("h1", 29);
        fieldMappings.put("h2", 34);
        fieldMappings.put("canonical", 39);
        fieldMappings.put("followFlg", 52);
        fieldMappings.put("indexable", 48);
        fieldMappings.put("contentType", 45);
        fieldMappings.put("responseCode", 80);
        fieldMappings.put("amphtmlHref", 2);
        
        List<HtmlChange> changes = HtmlChangeBuilder.buildFromMapEntities(previous, current, fieldMappings);
        
        log.info("Generated {} comprehensive changes:", changes.size());
        for (HtmlChange change : changes) {
            log.info("Change ID: {}, URL: {}, Domain: {}", 
                change.getChgId(), change.getUrl(), change.getDomainId());
        }
        log.info("");
    }
    
    /**
     * Example 3: SEO-focused comparison
     */
    public static void seoFocusedExample(HtmlClickHouseEntity previous, HtmlClickHouseEntity current) throws Exception {
        log.info("=== Example 3: SEO-Focused Entity Comparison ===");
        
        // Focus only on SEO-critical fields
        Map<String, Integer> seoFields = new HashMap<>();
        seoFields.put("title", 89);
        seoFields.put("description", 17);
        seoFields.put("canonical", 39);
        seoFields.put("indexable", 48);
        seoFields.put("followFlg", 52);
        seoFields.put("h1", 29);
        seoFields.put("responseCode", 80);
        
        List<HtmlChange> changes = HtmlChangeBuilder.buildFromMapEntities(previous, current, seoFields);
        
        log.info("Generated {} SEO-focused changes:", changes.size());
        for (HtmlChange change : changes) {
            String changeType = getSeoChangeType(change.getChgId());
            log.info("SEO Change - {}: '{}' → '{}'", 
                changeType,
                truncate(change.getPrevValue(), 30),
                truncate(change.getCurrValue(), 30));
        }
        log.info("");
    }
    
    /**
     * Example 4: Performance demonstration
     */
    public static void performanceExample(HtmlClickHouseEntity previous, HtmlClickHouseEntity current) throws Exception {
        log.info("=== Example 4: Performance Demonstration ===");
        
        String fieldMappingJson = "{\n" +
            "  \"title\": 89,\n" +
            "  \"description\": 17,\n" +
            "  \"h1\": 29,\n" +
            "  \"h2\": 34,\n" +
            "  \"canonical\": 39,\n" +
            "  \"followFlg\": 52,\n" +
            "  \"indexable\": 48,\n" +
            "  \"contentType\": 45,\n" +
            "  \"responseCode\": 80,\n" +
            "  \"amphtmlHref\": 2\n" +
            "}";
        
        int iterations = 1000;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < iterations; i++) {
            List<HtmlChange> changes = HtmlChangeBuilder.buildFromEntities(previous, current, fieldMappingJson);
            // Process changes (in real scenario)
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        log.info("Processed {} entity comparisons in {} ms", iterations, totalTime);
        log.info("Average time per comparison: {} ms", totalTime / (double) iterations);
        log.info("Comparisons per second: {}", (iterations * 1000L) / totalTime);
        log.info("");
    }
    
    /**
     * Create sample previous entity
     */
    private static HtmlClickHouseEntity createPreviousEntity() {
        HtmlClickHouseEntity entity = new HtmlClickHouseEntity();
        entity.setUrl("https://example.com/product/123");
        entity.setTrackDate(new Date());
        entity.setDomainId(12345);
        entity.setCrawlTimestamp(new Date(System.currentTimeMillis() - 86400000)); // 1 day ago
        entity.setUrlHash("prev_hash_123");
        entity.setUrlMurmurHash("prev_murmur_123");
        
        // Create previous crawler response
        CrawlerResponse response = new CrawlerResponse();
        response.setTitle("Original Product Title - Buy Now");
        response.setDescription("Original product description with old features");
        response.setH1(new String[]{"Original H1 Heading"});
        response.setH2(new String[]{"Original Feature 1", "Original Feature 2"});
        response.setContent_type("text/html");
        response.setFollow_flg("1"); // Follow
        response.setCanonical("https://example.com/product/123");
        response.setAmphtml_href("https://example.com/amp/product/123");
        response.setIndexable(false); // Not indexable initially
        response.setResponse_code("200");
        response.setDescription_length(45);
        response.setTitle_length(35);
        response.setH1_count(1);
        response.setOutlink_count(5);
        
        entity.setCrawlerResponse(response);
        return entity;
    }
    
    /**
     * Create sample current entity
     */
    private static HtmlClickHouseEntity createCurrentEntity() {
        HtmlClickHouseEntity entity = new HtmlClickHouseEntity();
        entity.setUrl("https://example.com/product/123");
        entity.setTrackDate(new Date());
        entity.setDomainId(12345);
        entity.setCrawlTimestamp(new Date());
        entity.setUrlHash("curr_hash_123");
        entity.setUrlMurmurHash("curr_murmur_123");
        
        // Create current crawler response with changes
        CrawlerResponse response = new CrawlerResponse();
        response.setTitle("Updated Product Title - Special Offer Available");
        response.setDescription("Updated product description with new features and benefits");
        response.setH1(new String[]{"Updated H1 Heading"});
        response.setH2(new String[]{"New Feature 1", "New Feature 2", "Additional Feature 3"});
        response.setContent_type("text/html; charset=utf-8");
        response.setFollow_flg("0"); // Changed to nofollow
        response.setCanonical("https://example.com/product/123/updated");
        response.setAmphtml_href("https://example.com/amp/product/123/new");
        response.setIndexable(true); // Now indexable
        response.setResponse_code("200");
        response.setDescription_length(65);
        response.setTitle_length(50);
        response.setH1_count(1);
        response.setOutlink_count(8);
        
        entity.setCrawlerResponse(response);
        return entity;
    }
    
    /**
     * Get SEO change type description
     */
    private static String getSeoChangeType(int changeId) {
        switch (changeId) {
            case 89: return "Title";
            case 17: return "Description";
            case 39: return "Canonical URL";
            case 48: return "Indexable Status";
            case 52: return "Follow Flag";
            case 29: return "H1 Tags";
            case 80: return "Response Code";
            default: return "Unknown (" + changeId + ")";
        }
    }
    
    /**
     * Utility method to truncate long strings for logging
     */
    private static String truncate(String str, int maxLength) {
        if (str == null) return "null";
        if (str.length() <= maxLength) return str;
        return str.substring(0, maxLength) + "...";
    }
}
