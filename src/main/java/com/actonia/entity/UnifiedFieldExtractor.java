package com.actonia.entity;

import com.actonia.value.object.CrawlerResponse;
import com.google.gson.Gson;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * Unified field extractor for HtmlClickHouseEntity objects
 * Supports extracting values from both entity fields and nested CrawlerResponse fields
 * Uses high-performance direct method calls instead of reflection
 */
public class UnifiedFieldExtractor {
    
    private static final Logger log = LogManager.getLogger(UnifiedFieldExtractor.class);
    private static final Gson gson = new Gson();
    
    // Function mappings for HtmlClickHouseEntity
    private final Map<String, Function<HtmlClickHouseEntity, String>> extractors;
    
    public UnifiedFieldExtractor() {
        this.extractors = initializeExtractors();
    }
    
    /**
     * Extract value from HtmlClickHouseEntity
     */
    public String extractValue(HtmlClickHouseEntity entity, String fieldName) {
        Function<HtmlClickHouseEntity, String> extractor = extractors.get(fieldName);
        if (extractor != null) {
            try {
                return extractor.apply(entity);
            } catch (Exception e) {
                log.warn("Error extracting field '{}' from entity: {}", fieldName, e.getMessage());
                return null;
            }
        }
        log.warn("No extractor found for field: {}", fieldName);
        return null;
    }
    
    /**
     * Initialize extractors for HtmlClickHouseEntity
     */
    private Map<String, Function<HtmlClickHouseEntity, String>> initializeExtractors() {
        Map<String, Function<HtmlClickHouseEntity, String>> extractors = new HashMap<>();
        
        // Fields from CrawlerResponse (most common)
        extractors.put("title", entity -> getCrawlerResponseField(entity, cr -> cr.getTitle()));
        extractors.put("description", entity -> getCrawlerResponseField(entity, cr -> cr.getDescription()));
        extractors.put("contentType", entity -> getCrawlerResponseField(entity, cr -> cr.getContent_type()));
        extractors.put("content_type", entity -> getCrawlerResponseField(entity, cr -> cr.getContent_type()));
        extractors.put("followFlg", entity -> getCrawlerResponseField(entity, cr -> cr.getFollow_flg()));
        extractors.put("follow_flg", entity -> getCrawlerResponseField(entity, cr -> cr.getFollow_flg()));
        extractors.put("canonical", entity -> getCrawlerResponseField(entity, cr -> cr.getCanonical()));
        extractors.put("amphtmlHref", entity -> getCrawlerResponseField(entity, cr -> cr.getAmphtml_href()));
        extractors.put("amphtml_href", entity -> getCrawlerResponseField(entity, cr -> cr.getAmphtml_href()));
        extractors.put("analyzedUrlS", entity -> getCrawlerResponseField(entity, cr -> cr.getAnalyzed_url_s()));
        extractors.put("analyzed_url_s", entity -> getCrawlerResponseField(entity, cr -> cr.getAnalyzed_url_s()));
        extractors.put("archiveFlg", entity -> getCrawlerResponseField(entity, cr -> cr.getArchive_flg()));
        extractors.put("archive_flg", entity -> getCrawlerResponseField(entity, cr -> cr.getArchive_flg()));
        extractors.put("baseTag", entity -> getCrawlerResponseField(entity, cr -> cr.getBase_tag()));
        extractors.put("base_tag", entity -> getCrawlerResponseField(entity, cr -> cr.getBase_tag()));
        extractors.put("baseTagTarget", entity -> getCrawlerResponseField(entity, cr -> cr.getBase_tag_target()));
        extractors.put("base_tag_target", entity -> getCrawlerResponseField(entity, cr -> cr.getBase_tag_target()));
        extractors.put("viewportContent", entity -> getCrawlerResponseField(entity, cr -> cr.getViewport_content()));
        extractors.put("viewport_content", entity -> getCrawlerResponseField(entity, cr -> cr.getViewport_content()));
        extractors.put("responseCode", entity -> getCrawlerResponseField(entity, cr -> cr.getResponse_code()));
        extractors.put("response_code", entity -> getCrawlerResponseField(entity, cr -> cr.getResponse_code()));
        
        // Array fields from CrawlerResponse
        extractors.put("h1", entity -> getCrawlerResponseField(entity, cr -> convertArrayToJson(cr.getH1())));
        extractors.put("h1_array", entity -> getCrawlerResponseField(entity, cr -> convertArrayToJson(cr.getH1())));
        extractors.put("h2", entity -> getCrawlerResponseField(entity, cr -> convertArrayToJson(cr.getH2())));
        extractors.put("h2_array", entity -> getCrawlerResponseField(entity, cr -> convertArrayToJson(cr.getH2())));
        extractors.put("customData", entity -> getCrawlerResponseField(entity, cr -> convertArrayToJson(cr.getCustom_data())));
        extractors.put("custom_data", entity -> getCrawlerResponseField(entity, cr -> convertArrayToJson(cr.getCustom_data())));
        
        // Boolean fields from CrawlerResponse
        extractors.put("amphtmlFlag", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getAmphtml_flag())));
        extractors.put("amphtml_flag", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getAmphtml_flag())));
        extractors.put("baseTagFlag", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getBase_tag_flag())));
        extractors.put("base_tag_flag", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getBase_tag_flag())));
        extractors.put("indexable", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getIndexable())));
        extractors.put("noodp", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getNoodp())));
        extractors.put("nosnippet", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getNosnippet())));
        extractors.put("noydir", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getNoydir())));
        extractors.put("canonicalFlg", entity -> getCrawlerResponseField(entity, cr -> cr.getCanonical_flg()));
        extractors.put("canonical_flg", entity -> getCrawlerResponseField(entity, cr -> cr.getCanonical_flg()));
        extractors.put("descriptionFlg", entity -> getCrawlerResponseField(entity, cr -> cr.getDescription_flg()));
        extractors.put("description_flg", entity -> getCrawlerResponseField(entity, cr -> cr.getDescription_flg()));
        extractors.put("h1Flg", entity -> getCrawlerResponseField(entity, cr -> cr.getH1_flg()));
        extractors.put("h1_flg", entity -> getCrawlerResponseField(entity, cr -> cr.getH1_flg()));
        extractors.put("indexFlg", entity -> getCrawlerResponseField(entity, cr -> cr.getIndex_flg()));
        extractors.put("index_flg", entity -> getCrawlerResponseField(entity, cr -> cr.getIndex_flg()));
        extractors.put("titleFlg", entity -> getCrawlerResponseField(entity, cr -> cr.getTitle_flg()));
        extractors.put("title_flg", entity -> getCrawlerResponseField(entity, cr -> cr.getTitle_flg()));
        extractors.put("canonicalHeaderFlag", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getCanonical_header_flag())));
        extractors.put("canonical_header_flag", entity -> getCrawlerResponseField(entity, cr -> convertBooleanToString(cr.getCanonical_header_flag())));
        
        // Integer fields from CrawlerResponse
        extractors.put("descriptionLength", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getDescription_length())));
        extractors.put("description_length", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getDescription_length())));
        extractors.put("h1Count", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getH1_count())));
        extractors.put("h1_count", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getH1_count())));
        extractors.put("h1Length", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getH1_length())));
        extractors.put("h1_length", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getH1_length())));
        extractors.put("outlinkCount", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getOutlink_count())));
        extractors.put("outlink_count", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getOutlink_count())));
        extractors.put("titleLength", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getTitle_length())));
        extractors.put("title_length", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getTitle_length())));
        extractors.put("redirectTimes", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getRedirect_times())));
        extractors.put("redirect_times", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getRedirect_times())));
        extractors.put("finalResponseCode", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getFinal_response_code())));
        extractors.put("final_response_code", entity -> getCrawlerResponseField(entity, cr -> convertIntegerToString(cr.getFinal_response_code())));
        
        // Additional string fields from CrawlerResponse
        extractors.put("canonicalType", entity -> getCrawlerResponseField(entity, cr -> cr.getCanonical_type()));
        extractors.put("canonical_type", entity -> getCrawlerResponseField(entity, cr -> cr.getCanonical_type()));
        extractors.put("canonicalHeaderType", entity -> getCrawlerResponseField(entity, cr -> cr.getCanonical_header_type()));
        extractors.put("canonical_header_type", entity -> getCrawlerResponseField(entity, cr -> cr.getCanonical_header_type()));
        extractors.put("errorMessage", entity -> getCrawlerResponseField(entity, cr -> cr.getError_message()));
        extractors.put("error_message", entity -> getCrawlerResponseField(entity, cr -> cr.getError_message()));
        extractors.put("redirectFinalUrl", entity -> getCrawlerResponseField(entity, cr -> cr.getRedirect_final_url()));
        extractors.put("redirect_final_url", entity -> getCrawlerResponseField(entity, cr -> cr.getRedirect_final_url()));
        extractors.put("blockedByRobots", entity -> getCrawlerResponseField(entity, cr -> cr.getBlocked_by_robots()));
        extractors.put("blocked_by_robots", entity -> getCrawlerResponseField(entity, cr -> cr.getBlocked_by_robots()));
        
        // Direct entity fields
        extractors.put("url", entity -> entity.getUrl());
        extractors.put("urlDomain", entity -> entity.getUrlDomain());
        extractors.put("url_domain", entity -> entity.getUrlDomain());
        extractors.put("urlHash", entity -> entity.getUrlHash());
        extractors.put("url_hash", entity -> entity.getUrlHash());
        extractors.put("urlMurmurHash", entity -> entity.getUrlMurmurHash());
        extractors.put("url_murmur_hash", entity -> entity.getUrlMurmurHash());
        extractors.put("changeTrackingHash", entity -> entity.getChangeTrackingHash());
        extractors.put("change_tracking_hash", entity -> entity.getChangeTrackingHash());
        
        // Entity boolean fields (change indicators)
        extractors.put("titleChgInd", entity -> convertBooleanToString(entity.getTitleChgInd()));
        extractors.put("title_chg_ind", entity -> convertBooleanToString(entity.getTitleChgInd()));
        extractors.put("descriptionChgInd", entity -> convertBooleanToString(entity.getDescriptionChgInd()));
        extractors.put("description_chg_ind", entity -> convertBooleanToString(entity.getDescriptionChgInd()));
        extractors.put("h1ChgInd", entity -> convertBooleanToString(entity.getH1ChgInd()));
        extractors.put("h1_chg_ind", entity -> convertBooleanToString(entity.getH1ChgInd()));
        extractors.put("h2ChgInd", entity -> convertBooleanToString(entity.getH2ChgInd()));
        extractors.put("h2_chg_ind", entity -> convertBooleanToString(entity.getH2ChgInd()));
        extractors.put("canonicalChgInd", entity -> convertBooleanToString(entity.getCanonicalChgInd()));
        extractors.put("canonical_chg_ind", entity -> convertBooleanToString(entity.getCanonicalChgInd()));
        extractors.put("responseCodeChgInd", entity -> convertBooleanToString(entity.getResponseCodeChgInd()));
        extractors.put("response_code_chg_ind", entity -> convertBooleanToString(entity.getResponseCodeChgInd()));
        
        // Entity integer fields
        extractors.put("domainId", entity -> convertIntegerToString(entity.getDomainId()));
        extractors.put("domain_id", entity -> convertIntegerToString(entity.getDomainId()));
        extractors.put("weekOfYear", entity -> convertIntegerToString(entity.getWeekOfYear()));
        extractors.put("week_of_year", entity -> convertIntegerToString(entity.getWeekOfYear()));
        extractors.put("internalLinkCount", entity -> convertIntegerToString(entity.getInternalLinkCount()));
        extractors.put("internal_link_count", entity -> convertIntegerToString(entity.getInternalLinkCount()));
        
        // Date fields
        extractors.put("trackDate", entity -> convertDateToString(entity.getTrackDate()));
        extractors.put("track_date", entity -> convertDateToString(entity.getTrackDate()));
        extractors.put("crawlTimestamp", entity -> convertDateToString(entity.getCrawlTimestamp()));
        extractors.put("crawl_timestamp", entity -> convertDateToString(entity.getCrawlTimestamp()));
        extractors.put("previousCrawlTimestamp", entity -> convertDateToString(entity.getPreviousCrawlTimestamp()));
        extractors.put("previous_crawl_timestamp", entity -> convertDateToString(entity.getPreviousCrawlTimestamp()));
        
        return extractors;
    }
    
    /**
     * Helper method to safely extract field from CrawlerResponse
     */
    private String getCrawlerResponseField(HtmlClickHouseEntity entity, Function<CrawlerResponse, String> extractor) {
        if (entity == null || entity.getCrawlerResponse() == null) {
            return null;
        }
        return extractor.apply(entity.getCrawlerResponse());
    }
    
    /**
     * Convert array to JSON string
     */
    private String convertArrayToJson(Object[] array) {
        if (array == null || array.length == 0) {
            return null;
        }
        return gson.toJson(array);
    }
    
    /**
     * Convert Boolean to String
     */
    private String convertBooleanToString(Boolean value) {
        if (value == null) {
            return null;
        }
        return value.toString();
    }
    
    /**
     * Convert Integer to String
     */
    private String convertIntegerToString(Integer value) {
        if (value == null) {
            return null;
        }
        return value.toString();
    }
    
    /**
     * Convert Date to String
     */
    private String convertDateToString(java.util.Date value) {
        if (value == null) {
            return null;
        }
        return value.toString();
    }
}
