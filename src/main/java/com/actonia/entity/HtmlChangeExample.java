package com.actonia.entity;

import com.actonia.value.object.CrawlerResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Example demonstrating the high-performance HTML Change Builder usage
 */
public class HtmlChangeExample {

    private static final Logger log = LogManager.getLogger(HtmlChangeExample.class);

    public static void main(String[] args) {
        try {
            // Create sample entities
            UrlMetricsEntityV3 previous = createSamplePreviousEntity();
            HtmlClickHouseEntity current = createSampleCurrentEntity();

            // Example 1: Basic JSON mapping
            basicJsonExample(previous, current);

            // Example 2: Comprehensive field mapping
            comprehensiveExample(previous, current);

            // Example 3: Custom field selection
            customFieldExample(previous, current);

            // Example 4: Performance demonstration
            performanceExample(previous, current);

        } catch (Exception e) {
            log.error("Error in example: {}", e.getMessage(), e);
        }
    }

    /**
     * Example 1: Basic JSON mapping for common fields
     */
    public static void basicJsonExample(UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) throws Exception {
        log.info("=== Example 1: Basic JSON Mapping ===");

        String fieldMappingJson = "{\n" +
            "  \"title\": 89,\n" +
            "  \"description\": 17,\n" +
            "  \"h1\": 29,\n" +
            "  \"h2\": 34\n" +
            "}";

        List<HtmlChange> changes = HtmlChangeBuilder.buildFromJson(previous, current, fieldMappingJson);

        log.info("Generated {} changes:", changes.size());
        for (HtmlChange change : changes) {
            log.info("Change ID: {}, Prev: '{}', Curr: '{}'",
                change.getChgId(),
                truncate(change.getPrevValue(), 50),
                truncate(change.getCurrValue(), 50));
        }
        log.info("");
    }

    /**
     * Example 2: Comprehensive field mapping
     */
    public static void comprehensiveExample(UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) throws Exception {
        log.info("=== Example 2: Comprehensive Field Mapping ===");

        Map<String, Integer> fieldMappings = HtmlChangeBuilder.createSampleFieldMappings();
        List<HtmlChange> changes = HtmlChangeBuilder.buildFromMap(previous, current, fieldMappings);

        log.info("Generated {} changes using sample mappings:", changes.size());
        for (HtmlChange change : changes) {
            log.info("Change ID: {}, URL: {}", change.getChgId(), change.getUrl());
        }
        log.info("");
    }

    /**
     * Example 3: Custom field selection
     */
    public static void customFieldExample(UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) throws Exception {
        log.info("=== Example 3: Custom Field Selection ===");

        // Focus only on SEO-critical fields
        Map<String, Integer> seoFields = new HashMap<>();
        seoFields.put("title", 89);
        seoFields.put("description", 17);
        seoFields.put("canonical", 39);
        seoFields.put("indexable", 48);
        seoFields.put("followFlg", 52);

        List<HtmlChange> changes = HtmlChangeBuilder.buildFromMap(previous, current, seoFields);

        log.info("Generated {} SEO-focused changes:", changes.size());
        for (HtmlChange change : changes) {
            log.info("SEO Change - ID: {}, Domain: {}", change.getChgId(), change.getDomainId());
        }
        log.info("");
    }

    /**
     * Example 4: Performance demonstration
     */
    public static void performanceExample(UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) throws Exception {
        log.info("=== Example 4: Performance Demonstration ===");

        String fieldMappingJson = "{\n" +
            "  \"title\": 89,\n" +
            "  \"description\": 17,\n" +
            "  \"h1\": 29,\n" +
            "  \"h2\": 34,\n" +
            "  \"contentType\": 45,\n" +
            "  \"followFlg\": 52,\n" +
            "  \"canonical\": 39,\n" +
            "  \"amphtmlHref\": 2,\n" +
            "  \"indexable\": 48,\n" +
            "  \"noodp\": 62\n" +
            "}";

        int iterations = 1000;
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < iterations; i++) {
            List<HtmlChange> changes = HtmlChangeBuilder.buildFromJson(previous, current, fieldMappingJson);
            // Process changes (in real scenario)
        }

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;

        log.info("Processed {} iterations in {} ms", iterations, totalTime);
        log.info("Average time per iteration: {} ms", totalTime / (double) iterations);
        log.info("Iterations per second: {}", (iterations * 1000L) / totalTime);
        log.info("");
    }

    /**
     * Create sample previous entity
     */
    private static UrlMetricsEntityV3 createSamplePreviousEntity() {
        UrlMetricsEntityV3 entity = new UrlMetricsEntityV3();
        entity.setUrl("https://example.com/page");
        entity.setTitle("Original Page Title");
        entity.setDescription("Original meta description for the page");
        entity.setH1_array(new String[]{"Original H1 Heading"});
        entity.setH2_array(new String[]{"Original H2 Section 1", "Original H2 Section 2"});
        entity.setContent_type("text/html");
        entity.setFollow_flg("1");
        entity.setCanonical("https://example.com/canonical");
        entity.setAmphtml_href("https://example.com/amp");
        entity.setResponse_code("200");
        entity.setCrawl_timestamp("2024-01-01 10:00:00");
        entity.setUrlType(1);
        entity.setDescription_length(45);
        entity.setTitle_length(20);
        return entity;
    }

    /**
     * Create sample current entity
     */
    private static HtmlClickHouseEntity createSampleCurrentEntity() {
        HtmlClickHouseEntity entity = new HtmlClickHouseEntity();
        entity.setUrl("https://example.com/page");
        entity.setTrackDate(new Date());
        entity.setDomainId(12345);
        entity.setCrawlTimestamp(new Date());

        // Create crawler response with changes
        CrawlerResponse response = new CrawlerResponse();
        response.setTitle("Updated Page Title - Now with More Keywords");
        response.setDescription("Updated meta description with better SEO optimization");
        response.setH1(new String[]{"Updated H1 Heading"});
        response.setH2(new String[]{"Updated H2 Section 1", "Updated H2 Section 2", "New H2 Section 3"});
        response.setContent_type("text/html; charset=utf-8");
        response.setFollow_flg("0"); // Changed from follow to nofollow
        response.setCanonical("https://example.com/new-canonical");
        response.setAmphtml_href("https://example.com/new-amp");
        response.setResponse_code("200");
        response.setIndexable(true);
        response.setNoodp(false);
        response.setDescription_length(55);
        response.setTitle_length(45);

        entity.setCrawlerResponse(response);
        return entity;
    }

    /**
     * Utility method to truncate long strings for logging
     */
    private static String truncate(String str, int maxLength) {
        if (str == null) return "null";
        if (str.length() <= maxLength) return str;
        return str.substring(0, maxLength) + "...";
    }
}
